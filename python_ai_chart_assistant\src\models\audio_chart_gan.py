"""
Audio-to-Chart GAN模型

使用对抗网络直接从MP3音频生成音游谱面
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import librosa
import numpy as np
from typing import Tuple, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class AudioEncoder(nn.Module):
    """音频编码器 - 提取音频特征"""
    
    def __init__(self, input_dim: int = 94, hidden_dim: int = 512):
        super().__init__()
        
        # 音频特征提取层
        self.conv_layers = nn.Sequential(
            # 第一层：时间维度卷积
            nn.Conv1d(input_dim, 256, kernel_size=3, padding=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.MaxPool1d(2),
            
            # 第二层：特征抽象
            nn.Conv1d(256, 512, kernel_size=3, padding=1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.MaxPool1d(2),
            
            # 第三层：高级特征
            nn.Conv1d(512, hidden_dim, kernel_size=3, padding=1),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8)
        
    def forward(self, audio_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            audio_features: [batch, features, time] 音频特征
            
        Returns:
            torch.Tensor: 编码后的音频表示
        """
        # 卷积特征提取
        x = self.conv_layers(audio_features)  # [batch, hidden_dim, time]
        
        # 转换为注意力机制的输入格式
        x = x.permute(2, 0, 1)  # [time, batch, hidden_dim]
        
        # 自注意力
        attended, _ = self.attention(x, x, x)
        
        return attended.permute(1, 0, 2)  # [batch, time, hidden_dim]


class ChartGenerator(nn.Module):
    """谱面生成器 - 从音频特征生成谱面"""
    
    def __init__(self, audio_dim: int = 512, track_count: int = 4, max_length: int = 2000):
        super().__init__()
        
        self.track_count = track_count
        self.max_length = max_length
        
        # 音频编码器
        self.audio_encoder = AudioEncoder(input_dim=94, hidden_dim=audio_dim)
        
        # 谱面生成网络
        self.chart_generator = nn.Sequential(
            nn.Linear(audio_dim, 1024),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            # 输出层：每个时间步的每个轨道的音符概率
            nn.Linear(512, track_count * 3)  # 3种音符类型：空白、短音符、长音符
        )
        
        # LSTM用于时序建模
        self.lstm = nn.LSTM(
            input_size=audio_dim,
            hidden_size=256,
            num_layers=2,
            batch_first=True,
            dropout=0.2,
            bidirectional=True
        )
        
        # 最终输出层
        self.output_layer = nn.Linear(512, track_count * 3)
        
    def forward(self, audio_features: torch.Tensor) -> torch.Tensor:
        """
        生成谱面
        
        Args:
            audio_features: [batch, features, time] 音频特征
            
        Returns:
            torch.Tensor: [batch, time, tracks*3] 谱面概率
        """
        # 编码音频特征
        encoded = self.audio_encoder(audio_features)  # [batch, time, hidden_dim]
        
        # LSTM时序建模
        lstm_out, _ = self.lstm(encoded)  # [batch, time, 512]
        
        # 生成谱面
        chart_logits = self.output_layer(lstm_out)  # [batch, time, tracks*3]
        
        # 重塑为 [batch, time, tracks, 3]
        batch_size, time_steps = chart_logits.shape[:2]
        chart_logits = chart_logits.view(batch_size, time_steps, self.track_count, 3)
        
        # 应用softmax获得概率分布
        chart_probs = F.softmax(chart_logits, dim=-1)
        
        return chart_probs


class ChartDiscriminator(nn.Module):
    """谱面判别器 - 判断谱面质量和真实性"""
    
    def __init__(self, track_count: int = 4, max_length: int = 2000):
        super().__init__()
        
        self.track_count = track_count
        
        # 谱面特征提取
        self.conv_layers = nn.Sequential(
            # 第一层：局部模式识别
            nn.Conv2d(1, 64, kernel_size=(3, track_count), padding=(1, 0)),
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2),
            
            # 第二层：时序模式
            nn.Conv2d(64, 128, kernel_size=(5, 1), padding=(2, 0)),
            nn.BatchNorm2d(128),
            nn.LeakyReLU(0.2),
            nn.MaxPool2d((2, 1)),
            
            # 第三层：复杂模式
            nn.Conv2d(128, 256, kernel_size=(3, 1), padding=(1, 0)),
            nn.BatchNorm2d(256),
            nn.LeakyReLU(0.2),
            nn.MaxPool2d((2, 1)),
        )
        
        # 全连接层（使用自适应池化来处理不同的输入尺寸）
        self.adaptive_pool = nn.AdaptiveAvgPool2d((1, 1))  # 将任意尺寸池化为1x1
        self.fc_layers = nn.Sequential(
            nn.Linear(256, 512),  # 256是最后一个卷积层的通道数
            nn.LeakyReLU(0.2),
            nn.Dropout(0.5),

            nn.Linear(512, 128),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.5),

            # 多任务输出
            nn.Linear(128, 64),
            nn.LeakyReLU(0.2),
        )
        
        # 输出头
        self.real_fake_head = nn.Linear(64, 1)  # 真假判别
        self.quality_head = nn.Linear(64, 1)    # 质量评分
        self.difficulty_head = nn.Linear(64, 10) # 难度分类
        
    def forward(self, chart: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        判别谱面
        
        Args:
            chart: [batch, time, tracks] 谱面数据
            
        Returns:
            Dict: 包含各种判别结果
        """
        batch_size = chart.shape[0]
        
        # 添加通道维度 [batch, 1, time, tracks]
        x = chart.unsqueeze(1)
        
        # 卷积特征提取
        x = self.conv_layers(x)  # [batch, 256, time//4, 1]

        # 使用自适应池化处理不同的时间长度
        x = self.adaptive_pool(x)  # [batch, 256, 1, 1]

        # 展平
        x = x.view(batch_size, -1)  # [batch, 256]

        # 全连接层
        features = self.fc_layers(x)  # [batch, 64]
        
        # 多任务输出
        real_fake = torch.sigmoid(self.real_fake_head(features))
        quality = torch.sigmoid(self.quality_head(features)) * 10  # 0-10分
        difficulty = F.softmax(self.difficulty_head(features), dim=1)
        
        return {
            'real_fake': real_fake,
            'quality': quality,
            'difficulty': difficulty
        }


class AudioChartGAN(nn.Module):
    """完整的Audio-to-Chart GAN模型"""
    
    def __init__(self, track_count: int = 4, max_length: int = 2000):
        super().__init__()
        
        self.generator = ChartGenerator(track_count=track_count, max_length=max_length)
        self.discriminator = ChartDiscriminator(track_count=track_count, max_length=max_length)
        
        # 损失函数
        self.adversarial_loss = nn.BCELoss()
        self.quality_loss = nn.MSELoss()
        self.difficulty_loss = nn.CrossEntropyLoss()
        
    def extract_audio_features(self, audio_path: str) -> torch.Tensor:
        """
        从MP3文件提取音频特征（与训练时保持一致）

        Args:
            audio_path: MP3文件路径

        Returns:
            torch.Tensor: 音频特征 [1, features, time]
        """
        # 加载音频
        y, sr = librosa.load(audio_path, sr=22050)

        # 提取多种特征（与训练时保持一致）
        features = []

        # 1. Mel频谱图 (64维)
        mel_spec = librosa.feature.melspectrogram(y=y, sr=sr, n_mels=64, hop_length=512)
        mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)
        features.append(mel_spec_db)

        # 2. MFCC (13维)
        mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13, hop_length=512)
        features.append(mfcc)

        # 3. 色度特征 (12维)
        chroma = librosa.feature.chroma_stft(y=y, sr=sr, hop_length=512)
        features.append(chroma)

        # 4. 频谱质心 (1维)
        spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr, hop_length=512)
        features.append(spectral_centroids)

        # 5. 频谱带宽 (1维)
        spectral_bandwidth = librosa.feature.spectral_bandwidth(y=y, sr=sr, hop_length=512)
        features.append(spectral_bandwidth)

        # 6. 频谱滚降 (1维)
        spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr, hop_length=512)
        features.append(spectral_rolloff)

        # 7. 零交叉率 (1维)
        zcr = librosa.feature.zero_crossing_rate(y, hop_length=512)
        features.append(zcr)

        # 8. 节拍跟踪 (1维)
        tempo, beats = librosa.beat.beat_track(y=y, sr=sr, hop_length=512)
        beat_features = np.zeros((1, mel_spec.shape[1]))
        beat_frames = librosa.time_to_frames(librosa.frames_to_time(beats), sr=sr, hop_length=512)
        for beat_frame in beat_frames:
            if beat_frame < beat_features.shape[1]:
                beat_features[0, beat_frame] = 1.0
        features.append(beat_features)

        # 合并所有特征
        combined_features = np.vstack(features)  # [94, time]
        
        # 转换为tensor
        return torch.FloatTensor(combined_features).unsqueeze(0)  # [1, features, time]
    
    def generate_chart(self, audio_path: str) -> np.ndarray:
        """
        从MP3文件生成谱面

        Args:
            audio_path: MP3文件路径

        Returns:
            np.ndarray: 生成的谱面
        """
        self.eval()
        with torch.no_grad():
            # 提取音频特征
            audio_features = self.extract_audio_features(audio_path)

            # 确保特征在正确的设备上
            device = next(self.parameters()).device
            audio_features = audio_features.to(device)

            # 生成谱面
            chart_probs = self.generator(audio_features)  # [1, time, tracks, 3]

            # 采样生成最终谱面
            chart = torch.argmax(chart_probs, dim=-1)  # [1, time, tracks]

            return chart.squeeze(0).cpu().numpy()  # [time, tracks]
