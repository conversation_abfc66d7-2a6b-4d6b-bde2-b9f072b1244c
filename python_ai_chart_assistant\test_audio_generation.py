#!/usr/bin/env python3
"""
测试音频到谱面生成
使用训练好的GAN模型从MP3生成谱面
"""

import torch
import sys
import os
from pathlib import Path
import logging

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from src.models.audio_chart_gan import AudioChartGAN
from src.chart_generation.chart_data import ChartData, ChartMetadata, NoteInfo
from src.format_converters.rhythm_master_converter import RhythmMasterConverter
from src.utils.logger_setup import setup_logging

def test_audio_generation():
    """测试从音频生成谱面"""

    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    # 配置
    audio_path = "mp3/a.mp3"
    model_path = "models/final_gan_model.pth"
    output_path = "output_test.imd"

    logger.info("🎵 开始测试音频到谱面生成")

    # 检查文件是否存在
    if not os.path.exists(audio_path):
        logger.error(f"❌ 音频文件不存在: {audio_path}")
        logger.info("💡 请将MP3文件放在 mp3/a.mp3")
        return False

    if not os.path.exists(model_path):
        logger.error(f"❌ 模型文件不存在: {model_path}")
        logger.info("💡 请确保训练完成并且模型文件存在")

        # 检查是否有其他模型文件
        models_dir = Path("models")
        if models_dir.exists():
            model_files = list(models_dir.glob("*.pth"))
            if model_files:
                logger.info("📁 找到的模型文件:")
                for model_file in model_files:
                    logger.info(f"   - {model_file}")
                logger.info("💡 您可以尝试使用其中一个模型文件")
        return False
    
    try:
        # 1. 加载训练好的模型
        logger.info("🤖 加载训练好的GAN模型...")
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        logger.info(f"🔧 使用设备: {device}")
        
        model = AudioChartGAN(track_count=4, max_length=2000)
        
        # 加载模型权重
        checkpoint = torch.load(model_path, map_location=device)

        # 检查保存的数据结构
        if isinstance(checkpoint, dict):
            if 'model_state_dict' in checkpoint:
                # 训练脚本保存的格式
                model_state = checkpoint['model_state_dict']
                if 'generator' in model_state:
                    model.generator.load_state_dict(model_state['generator'])
                    model.discriminator.load_state_dict(model_state['discriminator'])
                else:
                    model.load_state_dict(model_state)
            elif 'generator' in checkpoint:
                # 直接保存的生成器和判别器
                model.generator.load_state_dict(checkpoint['generator'])
                model.discriminator.load_state_dict(checkpoint['discriminator'])
            else:
                # 尝试直接加载
                model.load_state_dict(checkpoint)
        else:
            # 如果是直接的模型状态
            model.load_state_dict(checkpoint)
            
        model.to(device)
        model.eval()
        logger.info("✅ 模型加载成功")
        
        # 2. 从音频生成谱面
        logger.info(f"🎵 分析音频文件: {audio_path}")
        chart_array = model.generate_chart(audio_path)
        logger.info(f"📊 生成谱面数组形状: {chart_array.shape}")
        
        # 3. 转换为ChartData格式
        logger.info("🔄 转换谱面数据格式...")

        # 设置时间分辨率
        time_resolution = 0.125  # 时间分辨率（秒）

        # 创建元数据
        metadata = ChartMetadata(
            title="AI生成谱面",
            artist="AI Assistant",
            creator="AI Chart Generator",
            difficulty=7,
            bpm=120.0,  # 默认BPM，实际应该从音频分析获取
            duration=len(chart_array) * time_resolution,
            track_count=4
        )

        # 转换音符数据
        notes = []
        
        for time_idx, time_step in enumerate(chart_array):
            time_ms = int(time_idx * time_resolution * 1000)  # 转换为毫秒
            
            for track_idx, note_type in enumerate(time_step):
                if note_type > 0:  # 有音符
                    note = NoteInfo(
                        time=time_ms,
                        track=track_idx,
                        note_type="tap" if note_type == 1 else "hold",
                        duration=500 if note_type == 2 else 0  # 长音符持续时间
                    )
                    notes.append(note)
        
        # 创建谱面数据
        chart_data = ChartData(metadata=metadata)
        chart_data.add_notes(notes)
        
        logger.info(f"📝 生成了 {len(notes)} 个音符")
        
        # 4. 导出为节奏大师格式
        logger.info(f"💾 导出谱面到: {output_path}")
        converter = RhythmMasterConverter()
        success = converter.export(chart_data, output_path)
        
        if success:
            logger.info("🎉 谱面生成成功！")
            logger.info(f"📁 输出文件: {output_path}")
            
            # 显示统计信息
            logger.info("📊 谱面统计:")
            logger.info(f"   总时长: {chart_data.metadata.duration:.1f} 秒")
            logger.info(f"   音符数量: {len(notes)}")
            logger.info(f"   难度等级: {metadata.difficulty}")
            
            return True
        else:
            logger.error("❌ 谱面导出失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 生成过程出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    print("🎵 AI音游谱面生成测试")
    print("=" * 50)
    
    success = test_audio_generation()
    
    if success:
        print("\n🎉 测试成功完成！")
        print("💡 您可以将生成的.imd文件导入到节奏大师中使用")
    else:
        print("\n❌ 测试失败")
        print("💡 请检查:")
        print("   1. 音频文件是否存在: mp3/a.mp3")
        print("   2. 模型文件是否存在: models/final_gan_model.pth") 
        print("   3. 查看日志了解详细错误信息")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
