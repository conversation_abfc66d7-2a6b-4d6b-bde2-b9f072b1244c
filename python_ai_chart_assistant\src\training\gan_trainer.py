"""
GAN训练器

用于训练Audio-to-Chart GAN模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from pathlib import Path
import logging
from typing import Dict, Tuple, Optional
from tqdm import tqdm

from ..models.audio_chart_gan import AudioChartGAN
from .audio_chart_dataset import AudioChartDataset

logger = logging.getLogger(__name__)


class GANTrainer:
    """GAN训练器"""
    
    def __init__(
        self,
        model: AudioChartGAN,
        device: str = 'cuda',
        lr_g: float = 0.0002,
        lr_d: float = 0.0002,
        beta1: float = 0.5,
        beta2: float = 0.999
    ):
        self.model = model.to(device)
        self.device = device
        
        # 优化器
        self.optimizer_g = optim.Adam(
            self.model.generator.parameters(),
            lr=lr_g, betas=(beta1, beta2)
        )
        self.optimizer_d = optim.<PERSON>(
            self.model.discriminator.parameters(),
            lr=lr_d, betas=(beta1, beta2)
        )
        
        # 损失函数
        self.adversarial_loss = nn.BCELoss()
        self.quality_loss = nn.MSELoss()
        self.difficulty_loss = nn.CrossEntropyLoss()

        # 改进的损失权重
        self.loss_weights = {
            'adversarial': 1.0,
            'track_balance': 0.5,
            'note_diversity': 0.3,
            'consecutive_penalty': 0.2,
            'quality': 0.4
        }

        # 训练统计
        self.train_history = {
            'g_loss': [],
            'd_loss': [],
            'quality_score': [],
            'difficulty_acc': [],
            'track_balance': [],
            'note_diversity': []
        }

    def calculate_track_balance_loss(self, chart_probs: torch.Tensor) -> torch.Tensor:
        """
        计算轨道平衡损失

        Args:
            chart_probs: [batch, time, tracks, 3] 谱面概率

        Returns:
            torch.Tensor: 轨道平衡损失
        """
        # 计算每个轨道的音符总数
        # 取最大概率的音符类型（非空音符）
        note_presence = torch.max(chart_probs[:, :, :, 1:], dim=-1)[0]  # [batch, time, tracks]
        track_counts = torch.sum(note_presence, dim=1)  # [batch, tracks]

        # 计算轨道分布的方差（越小越均匀）
        track_mean = torch.mean(track_counts, dim=1, keepdim=True)  # [batch, 1]
        track_variance = torch.mean((track_counts - track_mean) ** 2, dim=1)  # [batch]

        return torch.mean(track_variance)

    def calculate_note_diversity_loss(self, chart_probs: torch.Tensor) -> torch.Tensor:
        """
        计算音符多样性损失

        Args:
            chart_probs: [batch, time, tracks, 3] 谱面概率

        Returns:
            torch.Tensor: 音符多样性损失
        """
        # 计算每种音符类型的总数
        note_type_counts = torch.sum(chart_probs, dim=(1, 2))  # [batch, 3]

        # 计算音符类型分布的熵（越大越多样）
        note_type_probs = F.softmax(note_type_counts, dim=1)
        entropy = -torch.sum(note_type_probs * torch.log(note_type_probs + 1e-8), dim=1)

        # 返回负熵作为损失（最大化多样性）
        return -torch.mean(entropy)

    def calculate_consecutive_penalty(self, chart_probs: torch.Tensor) -> torch.Tensor:
        """
        计算连击惩罚损失

        Args:
            chart_probs: [batch, time, tracks, 3] 谱面概率

        Returns:
            torch.Tensor: 连击惩罚损失
        """
        # 获取最可能的音符类型
        chart_pred = torch.argmax(chart_probs, dim=-1)  # [batch, time, tracks]

        # 计算连续音符的惩罚
        consecutive_penalty = 0.0

        for track in range(chart_pred.shape[2]):
            track_notes = chart_pred[:, :, track]  # [batch, time]

            # 检测连续的非空音符
            non_empty = (track_notes > 0).float()  # [batch, time]

            # 计算连续长度
            consecutive_lengths = []
            for batch in range(non_empty.shape[0]):
                current_length = 0
                for time_step in range(non_empty.shape[1]):
                    if non_empty[batch, time_step] > 0:
                        current_length += 1
                    else:
                        if current_length > 10:  # 超过10个连续音符开始惩罚
                            consecutive_penalty += (current_length - 10) ** 2
                        current_length = 0

                # 处理最后一段连续音符
                if current_length > 10:
                    consecutive_penalty += (current_length - 10) ** 2

        return torch.tensor(consecutive_penalty, device=chart_probs.device, dtype=torch.float32)

    def train_epoch(
        self, 
        dataloader: DataLoader, 
        epoch: int
    ) -> Dict[str, float]:
        """训练一个epoch"""
        
        self.model.train()
        epoch_stats = {
            'g_loss': 0.0,
            'd_loss': 0.0,
            'quality_score': 0.0,
            'difficulty_acc': 0.0
        }
        
        progress_bar = tqdm(dataloader, desc=f'Epoch {epoch}')
        
        for batch_idx, batch in enumerate(progress_bar):
            audio_features = batch['audio_features'].to(self.device)
            real_charts = batch['chart'].to(self.device)
            quality_scores = batch['quality'].to(self.device)
            difficulty_labels = batch['difficulty'].to(self.device)
            
            batch_size = audio_features.shape[0]
            
            # 真实和虚假标签
            real_labels = torch.ones(batch_size, 1).to(self.device)
            fake_labels = torch.zeros(batch_size, 1).to(self.device)
            
            # ==================
            # 训练判别器
            # ==================
            self.optimizer_d.zero_grad()
            
            # 真实谱面的判别
            real_output = self.model.discriminator(real_charts)
            d_real_loss = self.adversarial_loss(real_output['real_fake'], real_labels)
            
            # 质量和难度损失
            quality_loss = self.quality_loss(real_output['quality'], quality_scores)
            difficulty_loss = self.difficulty_loss(real_output['difficulty'], difficulty_labels)
            
            # 生成虚假谱面
            with torch.no_grad():
                fake_charts_probs = self.model.generator(audio_features)
                fake_charts = torch.argmax(fake_charts_probs, dim=-1).float()
            
            # 虚假谱面的判别
            fake_output = self.model.discriminator(fake_charts)
            d_fake_loss = self.adversarial_loss(fake_output['real_fake'], fake_labels)
            
            # 总判别器损失
            d_loss = (d_real_loss + d_fake_loss) / 2 + quality_loss + difficulty_loss
            d_loss.backward()
            self.optimizer_d.step()
            
            # ==================
            # 训练生成器
            # ==================
            self.optimizer_g.zero_grad()
            
            # 生成谱面
            generated_charts_probs = self.model.generator(audio_features)
            generated_charts = torch.argmax(generated_charts_probs, dim=-1).float()
            
            # 判别器对生成谱面的评价
            gen_output = self.model.discriminator(generated_charts)
            
            # 对抗损失：希望判别器认为生成的谱面是真的
            g_adversarial_loss = self.adversarial_loss(gen_output['real_fake'], real_labels)
            
            # 质量损失：希望生成高质量谱面
            g_quality_loss = self.quality_loss(gen_output['quality'], quality_scores)
            
            # 难度损失：希望生成符合预期难度的谱面
            g_difficulty_loss = self.difficulty_loss(gen_output['difficulty'], difficulty_labels)
            
            # 改进的损失函数
            density_loss = self._compute_density_loss(generated_charts_probs, real_charts)
            track_balance_loss = self.calculate_track_balance_loss(generated_charts_probs)
            note_diversity_loss = self.calculate_note_diversity_loss(generated_charts_probs)
            consecutive_penalty = self.calculate_consecutive_penalty(generated_charts_probs)

            # 总生成器损失（使用新的权重）
            g_loss = (self.loss_weights['adversarial'] * g_adversarial_loss +
                     self.loss_weights['quality'] * g_quality_loss +
                     0.3 * g_difficulty_loss +
                     0.2 * density_loss +
                     self.loss_weights['track_balance'] * track_balance_loss +
                     self.loss_weights['note_diversity'] * note_diversity_loss +
                     self.loss_weights['consecutive_penalty'] * consecutive_penalty)
            g_loss.backward()
            self.optimizer_g.step()
            
            # 统计
            epoch_stats['g_loss'] += g_loss.item()
            epoch_stats['d_loss'] += d_loss.item()
            epoch_stats['quality_score'] += gen_output['quality'].mean().item()

            # 难度准确率
            difficulty_pred = torch.argmax(gen_output['difficulty'], dim=1)
            difficulty_acc = (difficulty_pred == difficulty_labels).float().mean().item()
            epoch_stats['difficulty_acc'] += difficulty_acc

            # 新增统计
            if 'track_balance' not in epoch_stats:
                epoch_stats['track_balance'] = 0.0
                epoch_stats['note_diversity'] = 0.0

            epoch_stats['track_balance'] += track_balance_loss.item()
            epoch_stats['note_diversity'] += note_diversity_loss.item()
            
            # 更新进度条
            progress_bar.set_postfix({
                'G_Loss': f'{g_loss.item():.4f}',
                'D_Loss': f'{d_loss.item():.4f}',
                'Quality': f'{gen_output["quality"].mean().item():.2f}',
                'Diff_Acc': f'{difficulty_acc:.3f}'
            })
        
        # 计算平均值
        num_batches = len(dataloader)
        for key in epoch_stats:
            epoch_stats[key] /= num_batches
        
        return epoch_stats
    
    def _compute_density_loss(
        self, 
        generated_probs: torch.Tensor, 
        real_charts: torch.Tensor
    ) -> torch.Tensor:
        """
        计算音符密度损失
        
        Args:
            generated_probs: [batch, time, tracks, 3] 生成的概率
            real_charts: [batch, time, tracks] 真实谱面
            
        Returns:
            torch.Tensor: 密度损失
        """
        # 计算生成谱面的音符密度
        gen_notes = torch.argmax(generated_probs, dim=-1)  # [batch, time, tracks]
        gen_density = (gen_notes > 0).float().mean(dim=(1, 2))  # [batch]
        
        # 计算真实谱面的音符密度
        real_density = (real_charts > 0).float().mean(dim=(1, 2))  # [batch]
        
        # 密度差异损失
        density_loss = torch.abs(gen_density - real_density).mean()
        
        return density_loss
    
    def validate(self, val_dataloader: DataLoader) -> Dict[str, float]:
        """验证模型"""
        self.model.eval()
        val_stats = {
            'g_loss': 0.0,
            'd_loss': 0.0,
            'quality_score': 0.0,
            'difficulty_acc': 0.0
        }
        
        with torch.no_grad():
            for batch in val_dataloader:
                audio_features = batch['audio_features'].to(self.device)
                real_charts = batch['chart'].to(self.device)
                quality_scores = batch['quality'].to(self.device)
                difficulty_labels = batch['difficulty'].to(self.device)
                
                # 生成谱面
                generated_probs = self.model.generator(audio_features)
                generated_charts = torch.argmax(generated_probs, dim=-1).float()
                
                # 判别器评价
                gen_output = self.model.discriminator(generated_charts)
                real_output = self.model.discriminator(real_charts)
                
                # 计算损失
                batch_size = audio_features.shape[0]
                real_labels = torch.ones(batch_size, 1).to(self.device)
                fake_labels = torch.zeros(batch_size, 1).to(self.device)
                
                g_loss = self.adversarial_loss(gen_output['real_fake'], real_labels)
                d_loss = (
                    self.adversarial_loss(real_output['real_fake'], real_labels) +
                    self.adversarial_loss(gen_output['real_fake'], fake_labels)
                ) / 2
                
                val_stats['g_loss'] += g_loss.item()
                val_stats['d_loss'] += d_loss.item()
                val_stats['quality_score'] += gen_output['quality'].mean().item()
                
                # 难度准确率
                difficulty_pred = torch.argmax(gen_output['difficulty'], dim=1)
                difficulty_acc = (difficulty_pred == difficulty_labels).float().mean().item()
                val_stats['difficulty_acc'] += difficulty_acc
        
        # 计算平均值
        num_batches = len(val_dataloader)
        for key in val_stats:
            val_stats[key] /= num_batches
        
        return val_stats
    
    def train(
        self,
        train_dataloader: DataLoader,
        val_dataloader: Optional[DataLoader] = None,
        num_epochs: int = 100,
        save_dir: str = 'models',
        save_interval: int = 10
    ):
        """完整训练流程"""
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)
        
        best_quality = 0.0
        
        for epoch in range(1, num_epochs + 1):
            # 训练
            train_stats = self.train_epoch(train_dataloader, epoch)
            
            # 验证
            if val_dataloader:
                val_stats = self.validate(val_dataloader)
                logger.info(f"Epoch {epoch} - Train G: {train_stats['g_loss']:.4f}, "
                           f"D: {train_stats['d_loss']:.4f}, "
                           f"Val Quality: {val_stats['quality_score']:.2f}")
                
                # 保存最佳模型
                if val_stats['quality_score'] > best_quality:
                    best_quality = val_stats['quality_score']
                    torch.save(self.model.state_dict(), save_path / 'best_gan_model.pth')
            
            # 记录历史
            for key in train_stats:
                self.train_history[key].append(train_stats[key])
            
            # 定期保存
            if epoch % save_interval == 0:
                torch.save(self.model.state_dict(), save_path / f'gan_model_epoch_{epoch}.pth')
                
        logger.info(f"训练完成！最佳质量分数: {best_quality:.2f}")
    
    def save_model(self, path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_g_state_dict': self.optimizer_g.state_dict(),
            'optimizer_d_state_dict': self.optimizer_d.state_dict(),
            'train_history': self.train_history
        }, path)
    
    def load_model(self, path: str):
        """加载模型"""
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer_g.load_state_dict(checkpoint['optimizer_g_state_dict'])
        self.optimizer_d.load_state_dict(checkpoint['optimizer_d_state_dict'])
        self.train_history = checkpoint['train_history']
