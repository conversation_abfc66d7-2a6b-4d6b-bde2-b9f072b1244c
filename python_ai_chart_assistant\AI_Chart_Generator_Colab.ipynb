{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "source": ["# 🎵 AI音游谱面生成器 - Google Colab版\n", "\n", "这个笔记本可以让你在Google Colab中训练和使用AI谱面生成模型。\n", "\n", "## 🚀 特性\n", "- 从音频文件自动生成音游谱面\n", "- 支持4K/5K/6K等多种轨道模式\n", "- 改进的训练算法，解决轨道分布不均问题\n", "- 实时可视化生成效果\n", "\n", "## 📋 使用步骤\n", "1. 运行环境设置\n", "2. 上传项目文件和数据\n", "3. 训练或加载模型\n", "4. 生成谱面\n", "5. 下载结果"], "metadata": {"id": "title_cell"}}, {"cell_type": "markdown", "source": ["## 1. 🔧 环境设置"], "metadata": {"id": "setup_title"}}, {"cell_type": "code", "source": ["# 检查GPU状态\n", "import torch\n", "import sys\n", "\n", "print(\"🔥 GPU状态检查:\")\n", "print(f\"   CUDA可用: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"   GPU型号: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"   显存大小: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB\")\n", "    device = 'cuda'\n", "else:\n", "    print(\"   使用CPU模式\")\n", "    device = 'cpu'\n", "\n", "print(f\"\\n🎯 使用设备: {device}\")"], "metadata": {"id": "check_gpu"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# 安装依赖包\n", "print(\"📦 安装依赖包...\")\n", "\n", "!pip install librosa soundfile pydub matp<PERSON>lib seaborn tqdm pyyaml\n", "\n", "print(\"✅ 依赖安装完成\")"], "metadata": {"id": "install_deps"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 2. 📁 上传项目文件"], "metadata": {"id": "upload_title"}}, {"cell_type": "code", "source": ["# 上传项目zip文件\n", "from google.colab import files\n", "import zipfile\n", "import os\n", "\n", "print(\"📤 请上传项目zip文件 (python_ai_chart_assistant.zip)\")\n", "uploaded = files.upload()\n", "\n", "# 解压项目文件\n", "for filename in uploaded.keys():\n", "    if filename.endswith('.zip'):\n", "        print(f\"📂 解压 {filename}...\")\n", "        with zipfile.ZipFile(filename, 'r') as zip_ref:\n", "            zip_ref.extractall('.')\n", "        print(\"✅ 项目文件解压完成\")\n", "        break\n", "\n", "# 检查项目结构\n", "if os.path.exists('python_ai_chart_assistant'):\n", "    %cd python_ai_chart_assistant\n", "    print(\"📁 项目结构:\")\n", "    !ls -la\n", "else:\n", "    print(\"❌ 项目目录未找到，请检查zip文件\")"], "metadata": {"id": "upload_project"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# 上传模型和数据文件\n", "print(\"📤 请上传以下文件:\")\n", "print(\"1. 训练好的模型文件 (.pth)\")\n", "print(\"2. 音频文件 (.mp3)\")\n", "\n", "uploaded = files.upload()\n", "\n", "# 移动文件到正确位置\n", "import shutil\n", "from pathlib import Path\n", "\n", "# 创建必要目录\n", "Path('models').mkdir(exist_ok=True)\n", "Path('data/processed/audio').mkdir(parents=True, exist_ok=True)\n", "\n", "for filename in uploaded.keys():\n", "    if filename.endswith('.pth'):\n", "        shutil.move(filename, f'models/{filename}')\n", "        print(f\"✅ 模型文件移动到: models/{filename}\")\n", "    elif filename.endswith('.mp3'):\n", "        shutil.move(filename, f'data/processed/audio/{filename}')\n", "        print(f\"✅ 音频文件移动到: data/processed/audio/{filename}\")\n", "\n", "print(\"\\n📁 当前文件结构:\")\n", "!find models data -name \"*\" -type f 2>/dev/null || echo \"文件未找到\""], "metadata": {"id": "upload_data"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 3. 🤖 加载模型"], "metadata": {"id": "model_title"}}, {"cell_type": "code", "source": ["# 导入项目模块\n", "import sys\n", "sys.path.append('/content/python_ai_chart_assistant')\n", "\n", "from src.models.audio_chart_gan import AudioChartGAN\n", "import torch\n", "import numpy as np\n", "import json\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "\n", "print(\"📦 模块导入成功\")"], "metadata": {"id": "import_modules"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# 加载训练好的模型\n", "print(\"🤖 加载AI模型...\")\n", "\n", "# 创建模型实例\n", "model = AudioChartGAN(track_count=4, max_length=200).to(device)\n", "\n", "# 查找模型文件\n", "model_files = list(Path('models').glob('*.pth'))\n", "if model_files:\n", "    model_path = model_files[0]  # 使用第一个找到的模型文件\n", "    print(f\"📂 使用模型: {model_path}\")\n", "    \n", "    # 加载权重\n", "    checkpoint = torch.load(model_path, map_location=device)\n", "    model.load_state_dict(checkpoint['model_state_dict'])\n", "    model.eval()\n", "    \n", "    # 显示模型信息\n", "    total_params = sum(p.numel() for p in model.parameters())\n", "    print(f\"✅ 模型加载成功!\")\n", "    print(f\"📊 模型参数: {total_params:,}\")\n", "    \n", "    if 'best_quality' in checkpoint:\n", "        print(f\"🏆 最佳质量分数: {checkpoint['best_quality']:.4f}\")\n", "    \n", "else:\n", "    print(\"❌ 未找到模型文件，请先上传 .pth 文件\")"], "metadata": {"id": "load_model"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 4. 🎵 生成谱面"], "metadata": {"id": "generate_title"}}, {"cell_type": "code", "source": ["# 选择音频文件并生成谱面\n", "audio_files = list(Path('data/processed/audio').glob('*.mp3'))\n", "\n", "if audio_files:\n", "    audio_file = audio_files[0]  # 使用第一个音频文件\n", "    print(f\"🎵 使用音频文件: {audio_file.name}\")\n", "    \n", "    print(\"🔄 生成谱面中...\")\n", "    try:\n", "        # 生成谱面\n", "        chart_matrix = model.generate_chart(str(audio_file))\n", "        \n", "        print(f\"✅ 谱面生成成功!\")\n", "        print(f\"📊 谱面信息:\")\n", "        print(f\"   形状: {chart_matrix.shape}\")\n", "        print(f\"   时长: {chart_matrix.shape[0] * 0.1:.1f}秒\")\n", "        print(f\"   轨道数: {chart_matrix.shape[1]}\")\n", "        \n", "        # 分析谱面\n", "        total_notes = np.sum(chart_matrix > 0)\n", "        note_density = total_notes / chart_matrix.shape[0]\n", "        \n", "        print(f\"🎯 谱面统计:\")\n", "        print(f\"   总音符数: {total_notes}\")\n", "        print(f\"   音符密度: {note_density:.2f} 音符/时间步\")\n", "        \n", "        # 轨道分布\n", "        for track in range(chart_matrix.shape[1]):\n", "            track_notes = np.sum(chart_matrix[:, track] > 0)\n", "            percentage = track_notes / total_notes * 100 if total_notes > 0 else 0\n", "            print(f\"   轨道 {track}: {track_notes} 音符 ({percentage:.1f}%)\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ 生成失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        \n", "else:\n", "    print(\"❌ 未找到音频文件，请先上传 .mp3 文件\")"], "metadata": {"id": "generate_chart"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 5. 📊 可视化结果"], "metadata": {"id": "visualize_title"}}, {"cell_type": "code", "source": ["# 可视化生成的谱面\n", "if 'chart_matrix' in locals():\n", "    plt.figure(figsize=(16, 10))\n", "    \n", "    # 1. 谱面热力图\n", "    plt.subplot(3, 1, 1)\n", "    plt.imshow(chart_matrix.T, aspect='auto', cmap='viridis', origin='lower')\n", "    plt.title(f'AI生成的谱面 - {audio_file.stem}', fontsize=14)\n", "    plt.xlabel('时间步 (每步=0.1秒)')\n", "    plt.ylabel('轨道')\n", "    plt.colorbar(label='音符类型')\n", "    \n", "    # 2. 音符密度曲线\n", "    plt.subplot(3, 1, 2)\n", "    window_size = 20\n", "    density_curve = []\n", "    for i in range(0, chart_matrix.shape[0] - window_size, window_size//2):\n", "        window_notes = np.sum(chart_matrix[i:i+window_size] > 0)\n", "        density_curve.append(window_notes / window_size)\n", "    \n", "    plt.plot(density_curve, linewidth=2)\n", "    plt.title('音符密度变化')\n", "    plt.xlabel('时间窗口')\n", "    plt.ylabel('密度')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 3. 轨道分布饼图\n", "    plt.subplot(3, 1, 3)\n", "    track_counts = [np.sum(chart_matrix[:, i] > 0) for i in range(chart_matrix.shape[1])]\n", "    track_labels = [f'轨道 {i}' for i in range(chart_matrix.shape[1])]\n", "    \n", "    plt.pie(track_counts, labels=track_labels, autopct='%1.1f%%', startangle=90)\n", "    plt.title('轨道音符分布')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 显示改进建议\n", "    print(\"\\n💡 质量分析:\")\n", "    \n", "    # 轨道平衡分析\n", "    track_std = np.std(track_counts)\n", "    track_mean = np.mean(track_counts)\n", "    balance_score = 1 - (track_std / (track_mean + 1e-8))\n", "    \n", "    print(f\"   轨道平衡分数: {balance_score:.3f} (越接近1越好)\")\n", "    \n", "    if balance_score < 0.7:\n", "        print(\"   ⚠️ 轨道分布不均匀，建议继续训练改进\")\n", "    else:\n", "        print(\"   ✅ 轨道分布较为均匀\")\n", "    \n", "    # 音符类型分析\n", "    tap_notes = np.sum(chart_matrix == 1)\n", "    hold_notes = np.sum(chart_matrix == 2)\n", "    \n", "    print(f\"   点击音符: {tap_notes} ({tap_notes/total_notes*100:.1f}%)\")\n", "    print(f\"   长按音符: {hold_notes} ({hold_notes/total_notes*100:.1f}%)\")\n", "    \n", "    if hold_notes / total_notes < 0.1:\n", "        print(\"   ⚠️ 长按音符比例较低，建议增加多样性\")\n", "    \n", "else:\n", "    print(\"❌ 请先生成谱面\")"], "metadata": {"id": "visualize_chart"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 6. 💾 保存和下载结果"], "metadata": {"id": "save_title"}}, {"cell_type": "code", "source": ["# 保存生成的谱面为JSON格式\n", "if 'chart_matrix' in locals():\n", "    # 创建输出目录\n", "    output_dir = Path('colab_outputs')\n", "    output_dir.mkdir(exist_ok=True)\n", "    \n", "    # 转换为音符列表格式\n", "    notes = []\n", "    time_resolution = 0.1\n", "    \n", "    for time_step in range(chart_matrix.shape[0]):\n", "        for track in range(chart_matrix.shape[1]):\n", "            note_type = chart_matrix[time_step, track]\n", "            if note_type > 0:\n", "                note = {\n", "                    \"time\": round(time_step * time_resolution, 3),\n", "                    \"track\": int(track),\n", "                    \"note_type\": \"tap\" if note_type == 1 else \"hold\",\n", "                    \"duration\": 0.1 if note_type == 1 else 0.5,\n", "                    \"velocity\": 127\n", "                }\n", "                notes.append(note)\n", "    \n", "    # 创建完整的谱面数据\n", "    chart_data = {\n", "        \"metadata\": {\n", "            \"title\": audio_file.stem,\n", "            \"artist\": \"AI Generated (Colab)\",\n", "            \"difficulty\": 5,\n", "            \"bpm\": 120,\n", "            \"duration\": round(chart_matrix.shape[0] * time_resolution, 2),\n", "            \"track_count\": int(chart_matrix.shape[1]),\n", "            \"generated_on\": \"Google Colab\"\n", "        },\n", "        \"notes\": notes,\n", "        \"generation_info\": {\n", "            \"model\": \"AudioChartGAN\",\n", "            \"total_notes\": int(total_notes),\n", "            \"note_density\": float(note_density),\n", "            \"chart_shape\": list(chart_matrix.shape),\n", "            \"track_distribution\": track_counts,\n", "            \"balance_score\": float(balance_score)\n", "        }\n", "    }\n", "    \n", "    # 保存JSON文件\n", "    output_file = output_dir / f\"{audio_file.stem}_colab_generated.json\"\n", "    with open(output_file, 'w', encoding='utf-8') as f:\n", "        json.dump(chart_data, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(f\"✅ 谱面已保存: {output_file}\")\n", "    \n", "    # 创建分析报告\n", "    report_file = output_dir / f\"{audio_file.stem}_analysis_report.txt\"\n", "    with open(report_file, 'w', encoding='utf-8') as f:\n", "        f.write(f\"AI谱面生成分析报告\\n\")\n", "        f.write(f\"=\" * 30 + \"\\n\\n\")\n", "        f.write(f\"歌曲: {audio_file.stem}\\n\")\n", "        f.write(f\"生成时间: {chart_data['metadata']['duration']}秒\\n\")\n", "        f.write(f\"总音符数: {total_notes}\\n\")\n", "        f.write(f\"音符密度: {note_density:.2f}\\n\")\n", "        f.write(f\"轨道平衡分数: {balance_score:.3f}\\n\\n\")\n", "        \n", "        f.write(\"轨道分布:\\n\")\n", "        for i, count in enumerate(track_counts):\n", "            percentage = count / total_notes * 100 if total_notes > 0 else 0\n", "            f.write(f\"  轨道 {i}: {count} 音符 ({percentage:.1f}%)\\n\")\n", "        \n", "        f.write(f\"\\n音符类型:\\n\")\n", "        f.write(f\"  点击音符: {tap_notes} ({tap_notes/total_notes*100:.1f}%)\\n\")\n", "        f.write(f\"  长按音符: {hold_notes} ({hold_notes/total_notes*100:.1f}%)\\n\")\n", "    \n", "    print(f\"✅ 分析报告已保存: {report_file}\")\n", "    \n", "else:\n", "    print(\"❌ 请先生成谱面\")"], "metadata": {"id": "save_results"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# 打包并下载所有结果\n", "import zipfile\n", "from google.colab import files\n", "\n", "if output_dir.exists() and list(output_dir.glob('*')):\n", "    # 创建zip文件\n", "    zip_filename = 'ai_chart_generation_results.zip'\n", "    \n", "    with zipfile.ZipFile(zip_filename, 'w') as zipf:\n", "        for file_path in output_dir.glob('*'):\n", "            zipf.write(file_path, file_path.name)\n", "    \n", "    print(f\"📦 结果已打包: {zip_filename}\")\n", "    print(\"📥 开始下载...\")\n", "    \n", "    # 下载文件\n", "    files.download(zip_filename)\n", "    \n", "    print(\"✅ 下载完成!\")\n", "    \n", "else:\n", "    print(\"❌ 没有找到要下载的文件\")"], "metadata": {"id": "download_results"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 7. 🚀 改进训练 (可选)"], "metadata": {"id": "training_title"}}, {"cell_type": "code", "source": ["# 如果想要继续训练改进模型\n", "print(\"🚀 改进训练选项:\")\n", "print(\"\\n如果你想要改进模型质量，可以运行以下命令:\")\n", "print(\"\\n1. 使用改进的训练脚本:\")\n", "print(\"   !python train_improved_gan.py --epochs 30 --batch-size 8 --device cuda\")\n", "print(\"\\n2. 监控训练过程:\")\n", "print(\"   %load_ext tensorboard\")\n", "print(\"   %tensorboard --logdir logs/\")\n", "print(\"\\n3. 训练完成后重新加载模型并测试\")\n", "\n", "# 显示当前模型的改进建议\n", "if 'balance_score' in locals():\n", "    print(f\"\\n💡 基于当前结果的改进建议:\")\n", "    \n", "    if balance_score < 0.7:\n", "        print(\"   - 轨道分布不均匀，建议增加轨道平衡损失权重\")\n", "    \n", "    if hold_notes / total_notes < 0.1:\n", "        print(\"   - 长按音符比例低，建议增加音符多样性损失权重\")\n", "    \n", "    print(\"   - 可以尝试增加训练轮次\")\n", "    print(\"   - 可以调整学习率\")\n", "    print(\"   - 可以添加更多训练数据\")"], "metadata": {"id": "training_options"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 📝 使用说明\n", "\n", "### 🎯 快速开始\n", "1. 确保选择了GPU运行时 (Runtime → Change runtime type → GPU)\n", "2. 按顺序运行所有代码块\n", "3. 上传你的项目zip文件和模型文件\n", "4. 上传音频文件进行测试\n", "5. 查看生成结果并下载\n", "\n", "### 🔧 自定义选项\n", "- 修改 `track_count` 来支持不同的轨道数 (4K/5K/6K)\n", "- 调整 `max_length` 来处理不同长度的音频\n", "- 修改损失函数权重来改进生成质量\n", "\n", "### 📊 质量评估\n", "- **轨道平衡分数**: 越接近1越好 (>0.8为优秀)\n", "- **音符密度**: 5-15音符/秒为合适范围\n", "- **音符类型多样性**: 长按音符应占10-30%\n", "\n", "### 🚨 常见问题\n", "- **内存不足**: 减少batch_size或max_length\n", "- **生成质量差**: 增加训练轮次或调整损失权重\n", "- **轨道分布不均**: 启用轨道平衡损失函数\n", "\n", "### 📞 获取帮助\n", "如果遇到问题，请检查:\n", "1. 文件路径是否正确\n", "2. 模型文件是否完整\n", "3. GPU内存是否充足\n", "4. 依赖包是否正确安装"], "metadata": {"id": "instructions"}}]}